#!/usr/bin/env python3
"""
P9增强性能监控系统

该模块扩展P8性能监控功能，提供高级性能监控，包括：
1. 实时性能仪表板数据
2. 性能告警阈值配置
3. 历史性能趋势分析
4. 多维度性能指标分析
5. 自定义监控规则

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque
import threading
import time

# 导入P8性能监控器
try:
    from ..fusion.performance_monitor import PerformanceMonitor, PerformanceMetric, AlertRule
except ImportError:
    # 如果导入失败，定义基本类
    @dataclass
    class PerformanceMetric:
        name: str
        value: float
        timestamp: datetime
        category: str
        threshold: Optional[float] = None
        status: str = "normal"
    
    @dataclass
    class AlertRule:
        name: str
        metric_name: str
        condition: str
        threshold: float
        severity: str
        enabled: bool = True

class MonitoringLevel(Enum):
    """监控级别枚举"""
    BASIC = "basic"
    STANDARD = "standard"
    ADVANCED = "advanced"
    COMPREHENSIVE = "comprehensive"

class AlertSeverity(Enum):
    """告警严重程度枚举"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class DashboardMetric:
    """仪表板指标数据类"""
    metric_id: str
    display_name: str
    current_value: float
    previous_value: Optional[float]
    change_percentage: Optional[float]
    trend: str  # up/down/stable
    status: str  # good/warning/critical
    unit: str
    category: str
    timestamp: datetime

@dataclass
class PerformanceThreshold:
    """性能阈值配置"""
    metric_name: str
    warning_threshold: float
    critical_threshold: float
    comparison_operator: str  # >, <, >=, <=
    enabled: bool = True
    auto_adjust: bool = False
    baseline_period_days: int = 7

class EnhancedPerformanceMonitor:
    """增强性能监控系统 - P9扩展组件"""
    
    def __init__(self, db_path: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化增强性能监控系统
        
        Args:
            db_path: 数据库路径
            config: 配置参数
        """
        self.db_path = db_path
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 监控配置
        self.monitoring_config = {
            'monitoring_level': MonitoringLevel(self.config.get('monitoring_level', 'standard')),
            'collection_interval': self.config.get('collection_interval', 60),
            'retention_days': self.config.get('retention_days', 30),
            'dashboard_refresh_interval': self.config.get('dashboard_refresh_interval', 30),
            'trend_analysis_window': self.config.get('trend_analysis_window', 24),
            'auto_threshold_adjustment': self.config.get('auto_threshold_adjustment', True)
        }
        
        # 尝试初始化P8性能监控器
        self.p8_monitor = None
        self._initialize_p8_monitor()
        
        # 增强监控组件
        self.dashboard_data = {}
        self.performance_thresholds = {}
        self.custom_metrics = {}
        self.alert_history = deque(maxlen=1000)
        
        # 监控线程
        self.monitoring_thread = None
        self.monitoring_active = False
        
        # 性能缓存
        self.metrics_cache = defaultdict(deque)
        self.trend_cache = {}
        
        # 初始化组件
        self._initialize_thresholds()
        self._ensure_enhanced_tables()
        
        self.logger.info("增强性能监控系统初始化完成")
    
    def _initialize_p8_monitor(self):
        """初始化P8性能监控器"""
        try:
            # 尝试创建P8监控器实例
            monitor_config = {
                'monitoring_interval': self.monitoring_config['collection_interval'],
                'alert_thresholds': {
                    'accuracy': 0.3,
                    'hit_rate': 0.15,
                    'response_time': 5.0
                },
                'enabled_metrics': ['accuracy', 'hit_rate', 'mae', 'response_time']
            }
            
            if 'PerformanceMonitor' in globals():
                self.p8_monitor = PerformanceMonitor(self.db_path, monitor_config)
                self.logger.info("P8性能监控器初始化成功")
            else:
                self.logger.warning("P8性能监控器类不可用，使用独立模式")
                
        except Exception as e:
            self.logger.error(f"P8性能监控器初始化失败: {e}")
    
    def _initialize_thresholds(self):
        """初始化性能阈值"""
        default_thresholds = {
            'hundreds_accuracy': PerformanceThreshold(
                'hundreds_accuracy', 0.25, 0.20, '>', True, True, 7
            ),
            'tens_accuracy': PerformanceThreshold(
                'tens_accuracy', 0.25, 0.20, '>', True, True, 7
            ),
            'units_accuracy': PerformanceThreshold(
                'units_accuracy', 0.25, 0.20, '>', True, True, 7
            ),
            'fusion_hit_rate': PerformanceThreshold(
                'fusion_hit_rate', 0.12, 0.08, '>', True, True, 7
            ),
            'fusion_top10_hit_rate': PerformanceThreshold(
                'fusion_top10_hit_rate', 0.50, 0.40, '>', True, True, 7
            ),
            'sum_mae': PerformanceThreshold(
                'sum_mae', 2.5, 3.0, '<', True, True, 7
            ),
            'span_mae': PerformanceThreshold(
                'span_mae', 2.0, 2.5, '<', True, True, 7
            ),
            'response_time': PerformanceThreshold(
                'response_time', 3.0, 5.0, '<', True, False, 7
            ),
            'system_availability': PerformanceThreshold(
                'system_availability', 0.95, 0.90, '>', True, False, 7
            )
        }
        
        self.performance_thresholds.update(default_thresholds)
        self.logger.info(f"初始化了 {len(default_thresholds)} 个性能阈值")
    
    def _ensure_enhanced_tables(self):
        """确保增强监控表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建增强性能监控表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS enhanced_performance_monitor (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_id TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    metric_category TEXT NOT NULL,
                    component_name TEXT,
                    threshold_warning REAL,
                    threshold_critical REAL,
                    status TEXT NOT NULL,
                    trend_direction TEXT,
                    change_percentage REAL,
                    collection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT
                )
            """)
            
            # 创建性能阈值配置表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS performance_thresholds_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT UNIQUE NOT NULL,
                    warning_threshold REAL NOT NULL,
                    critical_threshold REAL NOT NULL,
                    comparison_operator TEXT NOT NULL,
                    enabled BOOLEAN DEFAULT TRUE,
                    auto_adjust BOOLEAN DEFAULT FALSE,
                    baseline_period_days INTEGER DEFAULT 7,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建仪表板配置表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS dashboard_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    dashboard_name TEXT NOT NULL,
                    metric_ids TEXT NOT NULL,
                    layout_config TEXT,
                    refresh_interval INTEGER DEFAULT 30,
                    enabled BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            
            self.logger.info("增强监控表创建成功")

        except Exception as e:
            self.logger.error(f"创建增强监控表失败: {e}")

    def start_enhanced_monitoring(self):
        """启动增强监控"""
        try:
            if self.monitoring_active:
                self.logger.warning("增强监控已在运行")
                return

            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()

            self.logger.info("增强性能监控已启动")

        except Exception as e:
            self.logger.error(f"启动增强监控失败: {e}")

    def stop_enhanced_monitoring(self):
        """停止增强监控"""
        try:
            self.monitoring_active = False
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)

            self.logger.info("增强性能监控已停止")

        except Exception as e:
            self.logger.error(f"停止增强监控失败: {e}")

    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集性能指标
                self._collect_performance_metrics()

                # 更新仪表板数据
                self._update_dashboard_data()

                # 检查告警条件
                self._check_alert_conditions()

                # 清理过期数据
                self._cleanup_expired_data()

                # 等待下一次收集
                time.sleep(self.monitoring_config['collection_interval'])

            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(10)  # 出错时等待10秒再继续

    def _collect_performance_metrics(self):
        """收集性能指标"""
        try:
            current_time = datetime.now()

            # 从P8监控器获取基础指标
            if self.p8_monitor:
                p8_metrics = self._get_p8_metrics()
                for metric in p8_metrics:
                    self._process_metric(metric, current_time)

            # 收集系统级指标
            system_metrics = self._collect_system_metrics()
            for metric in system_metrics:
                self._process_metric(metric, current_time)

            # 收集自定义指标
            custom_metrics = self._collect_custom_metrics()
            for metric in custom_metrics:
                self._process_metric(metric, current_time)

        except Exception as e:
            self.logger.error(f"收集性能指标失败: {e}")

    def _get_p8_metrics(self) -> List[PerformanceMetric]:
        """从P8监控器获取指标"""
        metrics = []
        try:
            if hasattr(self.p8_monitor, 'get_current_metrics'):
                p8_data = self.p8_monitor.get_current_metrics()
                for name, value in p8_data.items():
                    metric = PerformanceMetric(
                        name=name,
                        value=float(value),
                        timestamp=datetime.now(),
                        category="p8_metric"
                    )
                    metrics.append(metric)
        except Exception as e:
            self.logger.error(f"获取P8指标失败: {e}")

        return metrics

    def _collect_system_metrics(self) -> List[PerformanceMetric]:
        """收集系统级指标"""
        metrics = []
        current_time = datetime.now()

        try:
            # 查询数据库获取最新性能数据
            conn = sqlite3.connect(self.db_path)

            # 获取预测准确率
            accuracy_query = """
                SELECT
                    'hundreds_accuracy' as metric_name,
                    AVG(CASE WHEN predicted_hundreds = actual_hundreds THEN 1.0 ELSE 0.0 END) as accuracy
                FROM fusion_predictions
                WHERE prediction_time > datetime('now', '-1 hour')
                UNION ALL
                SELECT
                    'tens_accuracy' as metric_name,
                    AVG(CASE WHEN predicted_tens = actual_tens THEN 1.0 ELSE 0.0 END) as accuracy
                FROM fusion_predictions
                WHERE prediction_time > datetime('now', '-1 hour')
                UNION ALL
                SELECT
                    'units_accuracy' as metric_name,
                    AVG(CASE WHEN predicted_units = actual_units THEN 1.0 ELSE 0.0 END) as accuracy
                FROM fusion_predictions
                WHERE prediction_time > datetime('now', '-1 hour')
            """

            cursor = conn.execute(accuracy_query)
            accuracy_results = cursor.fetchall()

            for metric_name, accuracy in accuracy_results:
                if accuracy is not None:
                    metric = PerformanceMetric(
                        name=metric_name,
                        value=float(accuracy),
                        timestamp=current_time,
                        category="accuracy"
                    )
                    metrics.append(metric)

            # 获取命中率
            hit_rate_query = """
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN hit_status = 'hit' THEN 1 ELSE 0 END) as hits
                FROM fusion_predictions
                WHERE prediction_time > datetime('now', '-1 hour')
            """

            cursor = conn.execute(hit_rate_query)
            hit_result = cursor.fetchone()

            if hit_result and hit_result[0] > 0:
                hit_rate = hit_result[1] / hit_result[0]
                metric = PerformanceMetric(
                    name="fusion_hit_rate",
                    value=float(hit_rate),
                    timestamp=current_time,
                    category="hit_rate"
                )
                metrics.append(metric)

            conn.close()

        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")

        return metrics

    def _collect_custom_metrics(self) -> List[PerformanceMetric]:
        """收集自定义指标"""
        metrics = []
        current_time = datetime.now()

        try:
            # 系统可用性指标
            availability_metric = PerformanceMetric(
                name="system_availability",
                value=0.995,  # 模拟值
                timestamp=current_time,
                category="availability"
            )
            metrics.append(availability_metric)

            # 响应时间指标
            response_time_metric = PerformanceMetric(
                name="response_time",
                value=1.5,  # 模拟值
                timestamp=current_time,
                category="performance"
            )
            metrics.append(response_time_metric)

        except Exception as e:
            self.logger.error(f"收集自定义指标失败: {e}")

        return metrics

    def _process_metric(self, metric: PerformanceMetric, timestamp: datetime):
        """处理单个指标"""
        try:
            # 获取阈值配置
            threshold_config = self.performance_thresholds.get(metric.name)

            # 计算状态
            status = self._calculate_metric_status(metric.value, threshold_config)

            # 计算趋势
            trend, change_percentage = self._calculate_trend(metric.name, metric.value)

            # 更新缓存
            self.metrics_cache[metric.name].append({
                'value': metric.value,
                'timestamp': timestamp,
                'status': status,
                'trend': trend,
                'change_percentage': change_percentage
            })

            # 保持缓存大小
            if len(self.metrics_cache[metric.name]) > 100:
                self.metrics_cache[metric.name].popleft()

            # 保存到数据库
            self._save_metric_to_db(metric, status, trend, change_percentage)

        except Exception as e:
            self.logger.error(f"处理指标失败: {metric.name}, {e}")

    def _calculate_metric_status(self, value: float, threshold_config: Optional[PerformanceThreshold]) -> str:
        """计算指标状态"""
        if not threshold_config or not threshold_config.enabled:
            return "normal"

        try:
            operator = threshold_config.comparison_operator
            warning_threshold = threshold_config.warning_threshold
            critical_threshold = threshold_config.critical_threshold

            if operator == '>':
                if value < critical_threshold:
                    return "critical"
                elif value < warning_threshold:
                    return "warning"
                else:
                    return "good"
            elif operator == '<':
                if value > critical_threshold:
                    return "critical"
                elif value > warning_threshold:
                    return "warning"
                else:
                    return "good"
            elif operator == '>=':
                if value <= critical_threshold:
                    return "critical"
                elif value <= warning_threshold:
                    return "warning"
                else:
                    return "good"
            elif operator == '<=':
                if value >= critical_threshold:
                    return "critical"
                elif value >= warning_threshold:
                    return "warning"
                else:
                    return "good"

            return "normal"

        except Exception as e:
            self.logger.error(f"计算指标状态失败: {e}")
            return "unknown"

    def _calculate_trend(self, metric_name: str, current_value: float) -> Tuple[str, Optional[float]]:
        """计算趋势"""
        try:
            metric_history = self.metrics_cache.get(metric_name, deque())

            if len(metric_history) < 2:
                return "stable", None

            # 获取前一个值
            previous_value = metric_history[-1]['value']

            # 计算变化百分比
            if previous_value != 0:
                change_percentage = ((current_value - previous_value) / previous_value) * 100
            else:
                change_percentage = 0.0

            # 确定趋势方向
            if abs(change_percentage) < 1.0:  # 变化小于1%认为稳定
                trend = "stable"
            elif change_percentage > 0:
                trend = "up"
            else:
                trend = "down"

            return trend, change_percentage

        except Exception as e:
            self.logger.error(f"计算趋势失败: {e}")
            return "stable", None

    def _save_metric_to_db(self, metric: PerformanceMetric, status: str,
                          trend: str, change_percentage: Optional[float]):
        """保存指标到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            threshold_config = self.performance_thresholds.get(metric.name)

            cursor.execute("""
                INSERT INTO enhanced_performance_monitor
                (metric_id, metric_name, metric_value, metric_category, component_name,
                 threshold_warning, threshold_critical, status, trend_direction,
                 change_percentage, collection_time, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                f"{metric.name}_{int(metric.timestamp.timestamp())}",
                metric.name,
                metric.value,
                metric.category,
                getattr(metric, 'component', None),
                threshold_config.warning_threshold if threshold_config else None,
                threshold_config.critical_threshold if threshold_config else None,
                status,
                trend,
                change_percentage,
                metric.timestamp.isoformat(),
                json.dumps({'threshold_enabled': threshold_config.enabled if threshold_config else False})
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"保存指标到数据库失败: {e}")

    def _update_dashboard_data(self):
        """更新仪表板数据"""
        try:
            dashboard_metrics = {}

            for metric_name, metric_cache in self.metrics_cache.items():
                if not metric_cache:
                    continue

                latest_metric = metric_cache[-1]
                previous_metric = metric_cache[-2] if len(metric_cache) > 1 else None

                # 创建仪表板指标
                dashboard_metric = DashboardMetric(
                    metric_id=f"dashboard_{metric_name}",
                    display_name=self._get_display_name(metric_name),
                    current_value=latest_metric['value'],
                    previous_value=previous_metric['value'] if previous_metric else None,
                    change_percentage=latest_metric.get('change_percentage'),
                    trend=latest_metric.get('trend', 'stable'),
                    status=latest_metric.get('status', 'normal'),
                    unit=self._get_metric_unit(metric_name),
                    category=self._get_metric_category(metric_name),
                    timestamp=datetime.now()
                )

                dashboard_metrics[metric_name] = dashboard_metric

            self.dashboard_data = dashboard_metrics

        except Exception as e:
            self.logger.error(f"更新仪表板数据失败: {e}")

    def _get_display_name(self, metric_name: str) -> str:
        """获取指标显示名称"""
        display_names = {
            'hundreds_accuracy': '百位准确率',
            'tens_accuracy': '十位准确率',
            'units_accuracy': '个位准确率',
            'fusion_hit_rate': '融合命中率',
            'fusion_top10_hit_rate': 'Top10命中率',
            'sum_mae': '和值MAE',
            'span_mae': '跨度MAE',
            'response_time': '响应时间',
            'system_availability': '系统可用性'
        }
        return display_names.get(metric_name, metric_name)

    def _get_metric_unit(self, metric_name: str) -> str:
        """获取指标单位"""
        units = {
            'hundreds_accuracy': '%',
            'tens_accuracy': '%',
            'units_accuracy': '%',
            'fusion_hit_rate': '%',
            'fusion_top10_hit_rate': '%',
            'sum_mae': '',
            'span_mae': '',
            'response_time': 's',
            'system_availability': '%'
        }
        return units.get(metric_name, '')

    def _get_metric_category(self, metric_name: str) -> str:
        """获取指标类别"""
        categories = {
            'hundreds_accuracy': 'accuracy',
            'tens_accuracy': 'accuracy',
            'units_accuracy': 'accuracy',
            'fusion_hit_rate': 'hit_rate',
            'fusion_top10_hit_rate': 'hit_rate',
            'sum_mae': 'error',
            'span_mae': 'error',
            'response_time': 'performance',
            'system_availability': 'availability'
        }
        return categories.get(metric_name, 'other')

    def _check_alert_conditions(self):
        """检查告警条件"""
        try:
            for metric_name, metric_cache in self.metrics_cache.items():
                if not metric_cache:
                    continue

                latest_metric = metric_cache[-1]
                threshold_config = self.performance_thresholds.get(metric_name)

                if not threshold_config or not threshold_config.enabled:
                    continue

                # 检查是否需要告警
                if latest_metric['status'] in ['warning', 'critical']:
                    self._trigger_alert(metric_name, latest_metric, threshold_config)

        except Exception as e:
            self.logger.error(f"检查告警条件失败: {e}")

    def _trigger_alert(self, metric_name: str, metric_data: Dict[str, Any],
                      threshold_config: PerformanceThreshold):
        """触发告警"""
        try:
            alert_data = {
                'alert_id': f"alert_{metric_name}_{int(datetime.now().timestamp())}",
                'metric_name': metric_name,
                'current_value': metric_data['value'],
                'status': metric_data['status'],
                'threshold_warning': threshold_config.warning_threshold,
                'threshold_critical': threshold_config.critical_threshold,
                'timestamp': datetime.now().isoformat(),
                'message': f"{self._get_display_name(metric_name)}触发{metric_data['status']}告警"
            }

            # 添加到告警历史
            self.alert_history.append(alert_data)

            # 记录告警日志
            self.logger.warning(f"性能告警: {alert_data['message']}, 当前值: {metric_data['value']}")

        except Exception as e:
            self.logger.error(f"触发告警失败: {e}")

    def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.monitoring_config['retention_days'])

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清理过期的监控数据
            cursor.execute("""
                DELETE FROM enhanced_performance_monitor
                WHERE collection_time < ?
            """, (cutoff_date.isoformat(),))

            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()

            if deleted_count > 0:
                self.logger.info(f"清理了 {deleted_count} 条过期监控数据")

        except Exception as e:
            self.logger.error(f"清理过期数据失败: {e}")

    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        try:
            dashboard_summary = {
                'metrics': {},
                'summary': {
                    'total_metrics': len(self.dashboard_data),
                    'critical_count': 0,
                    'warning_count': 0,
                    'good_count': 0
                },
                'last_update': datetime.now().isoformat()
            }

            for metric_name, dashboard_metric in self.dashboard_data.items():
                # 转换为字典
                metric_dict = asdict(dashboard_metric)
                metric_dict['timestamp'] = dashboard_metric.timestamp.isoformat()
                dashboard_summary['metrics'][metric_name] = metric_dict

                # 统计状态
                if dashboard_metric.status == 'critical':
                    dashboard_summary['summary']['critical_count'] += 1
                elif dashboard_metric.status == 'warning':
                    dashboard_summary['summary']['warning_count'] += 1
                elif dashboard_metric.status == 'good':
                    dashboard_summary['summary']['good_count'] += 1

            return dashboard_summary

        except Exception as e:
            self.logger.error(f"获取仪表板数据失败: {e}")
            return {'error': str(e)}

    def get_performance_trends(self, metric_name: Optional[str] = None,
                             hours_back: int = 24) -> Dict[str, Any]:
        """获取性能趋势"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 构建查询条件
            where_clause = "WHERE collection_time > datetime('now', '-{} hours')".format(hours_back)
            if metric_name:
                where_clause += f" AND metric_name = '{metric_name}'"

            query = f"""
                SELECT metric_name, metric_value, status, trend_direction,
                       change_percentage, collection_time
                FROM enhanced_performance_monitor
                {where_clause}
                ORDER BY metric_name, collection_time
            """

            df = pd.read_sql_query(query, conn)
            conn.close()

            if df.empty:
                return {'message': '暂无趋势数据', 'trends': {}}

            # 按指标分组分析趋势
            trends = {}
            for metric, group in df.groupby('metric_name'):
                trend_analysis = self._analyze_metric_trend_from_df(group)
                trends[metric] = trend_analysis

            return {
                'trends': trends,
                'analysis_period_hours': hours_back,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"获取性能趋势失败: {e}")
            return {'error': str(e)}

    def _analyze_metric_trend_from_df(self, data: pd.DataFrame) -> Dict[str, Any]:
        """从DataFrame分析指标趋势"""
        try:
            if len(data) < 2:
                return {'trend': 'stable', 'confidence': 0.0}

            values = data['metric_value'].values

            # 计算趋势
            if len(values) >= 3:
                # 使用线性回归计算趋势
                x = np.arange(len(values))
                slope = np.polyfit(x, values, 1)[0]

                # 计算变异系数
                cv = np.std(values) / np.mean(values) if np.mean(values) != 0 else 0

                # 确定趋势方向
                if cv > 0.2:  # 变异系数大于20%认为波动
                    trend = "volatile"
                elif slope > 0.01:
                    trend = "improving"
                elif slope < -0.01:
                    trend = "declining"
                else:
                    trend = "stable"

                # 计算置信度
                confidence = min(1.0, len(values) / 10)

            else:
                trend = "stable"
                confidence = 0.5

            return {
                'trend': trend,
                'confidence': confidence,
                'current_value': float(values[-1]),
                'average_value': float(np.mean(values)),
                'min_value': float(np.min(values)),
                'max_value': float(np.max(values)),
                'sample_count': len(values),
                'volatility': float(cv) if len(values) >= 3 else 0.0
            }

        except Exception as e:
            self.logger.error(f"分析指标趋势失败: {e}")
            return {'trend': 'stable', 'confidence': 0.0, 'error': str(e)}

    def update_threshold_config(self, metric_name: str, threshold_config: PerformanceThreshold) -> bool:
        """更新阈值配置"""
        try:
            # 更新内存配置
            self.performance_thresholds[metric_name] = threshold_config

            # 更新数据库配置
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT OR REPLACE INTO performance_thresholds_config
                (metric_name, warning_threshold, critical_threshold, comparison_operator,
                 enabled, auto_adjust, baseline_period_days, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                threshold_config.metric_name,
                threshold_config.warning_threshold,
                threshold_config.critical_threshold,
                threshold_config.comparison_operator,
                threshold_config.enabled,
                threshold_config.auto_adjust,
                threshold_config.baseline_period_days,
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"阈值配置已更新: {metric_name}")
            return True

        except Exception as e:
            self.logger.error(f"更新阈值配置失败: {e}")
            return False

    def get_threshold_config(self, metric_name: Optional[str] = None) -> Dict[str, Any]:
        """获取阈值配置"""
        try:
            if metric_name:
                threshold = self.performance_thresholds.get(metric_name)
                if threshold:
                    return asdict(threshold)
                else:
                    return {'error': f'未找到指标 {metric_name} 的阈值配置'}
            else:
                # 返回所有阈值配置
                all_thresholds = {}
                for name, threshold in self.performance_thresholds.items():
                    all_thresholds[name] = asdict(threshold)
                return all_thresholds

        except Exception as e:
            self.logger.error(f"获取阈值配置失败: {e}")
            return {'error': str(e)}

    def get_alert_history(self, hours_back: int = 24, severity: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取告警历史"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)

            filtered_alerts = []
            for alert in self.alert_history:
                alert_time = datetime.fromisoformat(alert['timestamp'])
                if alert_time > cutoff_time:
                    if not severity or alert.get('status') == severity:
                        filtered_alerts.append(alert)

            # 按时间倒序排列
            filtered_alerts.sort(key=lambda x: x['timestamp'], reverse=True)

            return filtered_alerts

        except Exception as e:
            self.logger.error(f"获取告警历史失败: {e}")
            return []

    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        try:
            status = {
                'monitoring_active': self.monitoring_active,
                'monitoring_level': self.monitoring_config['monitoring_level'].value,
                'collection_interval': self.monitoring_config['collection_interval'],
                'retention_days': self.monitoring_config['retention_days'],
                'p8_monitor_available': self.p8_monitor is not None,
                'metrics_count': len(self.metrics_cache),
                'dashboard_metrics_count': len(self.dashboard_data),
                'thresholds_count': len(self.performance_thresholds),
                'recent_alerts_count': len([
                    alert for alert in self.alert_history
                    if datetime.fromisoformat(alert['timestamp']) > datetime.now() - timedelta(hours=1)
                ]),
                'last_collection': max([
                    max(cache, key=lambda x: x['timestamp'])['timestamp']
                    for cache in self.metrics_cache.values() if cache
                ], default=None),
                'uptime_seconds': (datetime.now() - datetime.now()).total_seconds() if self.monitoring_active else 0
            }

            return status

        except Exception as e:
            self.logger.error(f"获取监控状态失败: {e}")
            return {'error': str(e)}

    def export_performance_data(self, metric_names: Optional[List[str]] = None,
                               start_time: Optional[datetime] = None,
                               end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """导出性能数据"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 构建查询条件
            where_conditions = []
            params = []

            if metric_names:
                placeholders = ','.join(['?' for _ in metric_names])
                where_conditions.append(f"metric_name IN ({placeholders})")
                params.extend(metric_names)

            if start_time:
                where_conditions.append("collection_time >= ?")
                params.append(start_time.isoformat())

            if end_time:
                where_conditions.append("collection_time <= ?")
                params.append(end_time.isoformat())

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            query = f"""
                SELECT metric_name, metric_value, metric_category, status,
                       trend_direction, change_percentage, collection_time
                FROM enhanced_performance_monitor
                {where_clause}
                ORDER BY metric_name, collection_time
            """

            df = pd.read_sql_query(query, conn, params=params)
            conn.close()

            if df.empty:
                return {'message': '暂无数据', 'data': []}

            # 转换为字典格式
            export_data = df.to_dict('records')

            return {
                'data': export_data,
                'total_records': len(export_data),
                'metrics_included': df['metric_name'].unique().tolist(),
                'time_range': {
                    'start': df['collection_time'].min(),
                    'end': df['collection_time'].max()
                },
                'export_time': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"导出性能数据失败: {e}")
            return {'error': str(e)}

    def get_system_health_summary(self) -> Dict[str, Any]:
        """获取系统健康摘要"""
        try:
            # 统计各状态的指标数量
            status_counts = {'good': 0, 'warning': 0, 'critical': 0, 'unknown': 0}

            for dashboard_metric in self.dashboard_data.values():
                status = dashboard_metric.status
                if status in status_counts:
                    status_counts[status] += 1
                else:
                    status_counts['unknown'] += 1

            # 计算健康分数
            total_metrics = sum(status_counts.values())
            if total_metrics > 0:
                health_score = (
                    status_counts['good'] * 1.0 +
                    status_counts['warning'] * 0.5 +
                    status_counts['critical'] * 0.0 +
                    status_counts['unknown'] * 0.3
                ) / total_metrics
            else:
                health_score = 0.0

            # 确定健康等级
            if health_score >= 0.8:
                health_level = "excellent"
            elif health_score >= 0.6:
                health_level = "good"
            elif health_score >= 0.4:
                health_level = "fair"
            else:
                health_level = "poor"

            return {
                'health_score': health_score,
                'health_level': health_level,
                'status_distribution': status_counts,
                'total_metrics': total_metrics,
                'monitoring_active': self.monitoring_active,
                'recent_alerts': len(self.get_alert_history(hours_back=1)),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"获取系统健康摘要失败: {e}")
            return {'error': str(e)}
