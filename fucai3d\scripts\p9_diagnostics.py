#!/usr/bin/env python3
"""
P9系统诊断工具

该脚本提供P9闭环自动优化系统的深度诊断功能，包括：
1. 性能瓶颈分析
2. 错误模式识别
3. 资源使用分析
4. 预测质量评估
5. 系统优化建议
6. 故障排除指南

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import os
import sys
import sqlite3
import json
import logging
import argparse
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import re

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class P9SystemDiagnostics:
    """P9系统诊断器"""
    
    def __init__(self, verbose: bool = False):
        """
        初始化诊断器
        
        Args:
            verbose: 是否详细输出
        """
        self.project_root = project_root
        self.verbose = verbose
        
        # 设置日志
        self._setup_logging()
        
        # 诊断结果
        self.diagnostic_results = {}
        
        self.logger.info("P9系统诊断器初始化完成")
    
    def _setup_logging(self):
        """设置日志"""
        log_level = logging.DEBUG if self.verbose else logging.INFO
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def run_full_diagnostics(self) -> Dict[str, Any]:
        """运行完整诊断"""
        try:
            print("🔬 开始P9系统深度诊断...")
            print("="*60)
            
            # 1. 性能瓶颈分析
            self.diagnostic_results['performance_bottlenecks'] = self._analyze_performance_bottlenecks()
            
            # 2. 错误模式识别
            self.diagnostic_results['error_patterns'] = self._identify_error_patterns()
            
            # 3. 资源使用分析
            self.diagnostic_results['resource_usage'] = self._analyze_resource_usage()
            
            # 4. 预测质量评估
            self.diagnostic_results['prediction_quality'] = self._assess_prediction_quality()
            
            # 5. 数据质量分析
            self.diagnostic_results['data_quality'] = self._analyze_data_quality()
            
            # 6. 系统配置分析
            self.diagnostic_results['configuration_analysis'] = self._analyze_system_configuration()
            
            # 7. 生成诊断报告
            self.diagnostic_results['diagnostic_report'] = self._generate_diagnostic_report()
            
            # 打印诊断摘要
            self._print_diagnostic_summary()
            
            return self.diagnostic_results
            
        except Exception as e:
            self.logger.error(f"诊断过程失败: {e}")
            return {'error': str(e), 'traceback': traceback.format_exc()}
    
    def _analyze_performance_bottlenecks(self) -> Dict[str, Any]:
        """分析性能瓶颈"""
        try:
            print("⚡ 分析性能瓶颈...")
            
            db_path = self.project_root / 'data' / 'fucai3d.db'
            
            if not db_path.exists():
                return {'status': 'error', 'error': '数据库文件不存在'}
            
            conn = sqlite3.connect(db_path)
            
            bottlenecks = {}
            
            # 分析预测响应时间
            try:
                response_time_query = """
                    SELECT 
                        DATE(prediction_time) as date,
                        AVG(julianday(prediction_time) - julianday(LAG(prediction_time) OVER (ORDER BY prediction_time))) * 24 * 3600 as avg_interval_seconds,
                        COUNT(*) as prediction_count
                    FROM fusion_predictions 
                    WHERE prediction_time > datetime('now', '-7 days')
                    GROUP BY DATE(prediction_time)
                    ORDER BY date DESC
                """
                
                df_response = pd.read_sql_query(response_time_query, conn)
                
                if not df_response.empty:
                    avg_interval = df_response['avg_interval_seconds'].mean()
                    max_interval = df_response['avg_interval_seconds'].max()
                    
                    bottlenecks['prediction_timing'] = {
                        'average_interval_seconds': round(avg_interval, 2) if pd.notna(avg_interval) else None,
                        'max_interval_seconds': round(max_interval, 2) if pd.notna(max_interval) else None,
                        'daily_prediction_counts': df_response.to_dict('records'),
                        'status': 'slow' if avg_interval and avg_interval > 300 else 'normal'
                    }
                
            except Exception as e:
                bottlenecks['prediction_timing'] = {'error': str(e)}
            
            # 分析数据库查询性能
            try:
                # 检查表大小
                table_size_query = """
                    SELECT 
                        name as table_name,
                        COUNT(*) as row_count
                    FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """
                
                cursor = conn.cursor()
                cursor.execute(table_size_query)
                tables = cursor.fetchall()
                
                table_sizes = {}
                for table_name, _ in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        row_count = cursor.fetchone()[0]
                        table_sizes[table_name] = row_count
                    except Exception:
                        table_sizes[table_name] = 'error'
                
                # 识别大表
                large_tables = {name: count for name, count in table_sizes.items() 
                              if isinstance(count, int) and count > 10000}
                
                bottlenecks['database_performance'] = {
                    'table_sizes': table_sizes,
                    'large_tables': large_tables,
                    'total_tables': len(tables),
                    'status': 'concern' if large_tables else 'normal'
                }
                
            except Exception as e:
                bottlenecks['database_performance'] = {'error': str(e)}
            
            # 分析内存使用模式
            try:
                import psutil
                
                process = psutil.Process()
                memory_info = process.memory_info()
                
                bottlenecks['memory_usage'] = {
                    'rss_mb': round(memory_info.rss / (1024*1024), 2),
                    'vms_mb': round(memory_info.vms / (1024*1024), 2),
                    'memory_percent': round(process.memory_percent(), 2),
                    'status': 'high' if process.memory_percent() > 80 else 'normal'
                }
                
            except Exception as e:
                bottlenecks['memory_usage'] = {'error': str(e)}
            
            conn.close()
            
            # 评估整体性能状态
            performance_issues = []
            
            for component, data in bottlenecks.items():
                if isinstance(data, dict) and data.get('status') in ['slow', 'concern', 'high']:
                    performance_issues.append(component)
            
            overall_status = 'issues_found' if performance_issues else 'normal'
            
            result = {
                'status': overall_status,
                'bottlenecks': bottlenecks,
                'performance_issues': performance_issues,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"  ✅ 性能瓶颈分析完成: {overall_status}")
            if performance_issues:
                print(f"  ⚠️  发现问题: {', '.join(performance_issues)}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"性能瓶颈分析失败: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _identify_error_patterns(self) -> Dict[str, Any]:
        """识别错误模式"""
        try:
            print("🔍 识别错误模式...")
            
            logs_dir = self.project_root / 'logs'
            
            if not logs_dir.exists():
                return {'status': 'no_logs', 'message': '日志目录不存在'}
            
            error_patterns = {}
            
            # 分析日志文件中的错误
            log_files = list(logs_dir.glob('*.log'))
            
            all_errors = []
            error_counts = Counter()
            error_trends = defaultdict(list)
            
            for log_file in log_files:
                try:
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                    
                    # 提取错误信息
                    for line_num, line in enumerate(lines, 1):
                        if any(level in line for level in ['ERROR', 'CRITICAL', 'EXCEPTION']):
                            # 尝试提取时间戳
                            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2}[\s_]\d{2}:\d{2}:\d{2})', line)
                            timestamp = timestamp_match.group(1) if timestamp_match else None
                            
                            # 提取错误类型
                            error_type = 'Unknown'
                            if 'ImportError' in line:
                                error_type = 'ImportError'
                            elif 'FileNotFoundError' in line:
                                error_type = 'FileNotFoundError'
                            elif 'DatabaseError' in line or 'sqlite3' in line:
                                error_type = 'DatabaseError'
                            elif 'MemoryError' in line:
                                error_type = 'MemoryError'
                            elif 'TimeoutError' in line:
                                error_type = 'TimeoutError'
                            elif 'ValueError' in line:
                                error_type = 'ValueError'
                            elif 'KeyError' in line:
                                error_type = 'KeyError'
                            
                            error_info = {
                                'file': log_file.name,
                                'line_number': line_num,
                                'timestamp': timestamp,
                                'error_type': error_type,
                                'message': line.strip()[:200]  # 限制长度
                            }
                            
                            all_errors.append(error_info)
                            error_counts[error_type] += 1
                            
                            # 按日期分组错误趋势
                            if timestamp:
                                try:
                                    date = timestamp.split()[0]
                                    error_trends[date].append(error_type)
                                except Exception:
                                    pass
                
                except Exception as e:
                    self.logger.warning(f"分析日志文件失败 {log_file}: {e}")
            
            # 分析错误模式
            error_patterns['error_summary'] = {
                'total_errors': len(all_errors),
                'unique_error_types': len(error_counts),
                'most_common_errors': dict(error_counts.most_common(10)),
                'error_distribution': dict(error_counts)
            }
            
            # 分析错误趋势
            daily_error_counts = {}
            for date, errors in error_trends.items():
                daily_error_counts[date] = len(errors)
            
            error_patterns['error_trends'] = {
                'daily_counts': daily_error_counts,
                'trend_analysis': self._analyze_error_trend(daily_error_counts)
            }
            
            # 识别关键错误
            critical_errors = [error for error in all_errors 
                             if error['error_type'] in ['DatabaseError', 'MemoryError', 'ImportError']]
            
            error_patterns['critical_errors'] = {
                'count': len(critical_errors),
                'recent_critical': critical_errors[-5:] if critical_errors else [],
                'critical_types': Counter([e['error_type'] for e in critical_errors])
            }
            
            # 评估错误严重程度
            severity = 'low'
            if len(all_errors) > 100:
                severity = 'high'
            elif len(all_errors) > 20:
                severity = 'medium'
            elif len(critical_errors) > 0:
                severity = 'medium'
            
            result = {
                'status': 'analyzed',
                'severity': severity,
                'patterns': error_patterns,
                'recommendations': self._generate_error_recommendations(error_patterns),
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"  ✅ 错误模式识别完成: {len(all_errors)} 个错误，严重程度: {severity}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"错误模式识别失败: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _analyze_error_trend(self, daily_counts: Dict[str, int]) -> Dict[str, Any]:
        """分析错误趋势"""
        try:
            if len(daily_counts) < 2:
                return {'trend': 'insufficient_data'}
            
            dates = sorted(daily_counts.keys())
            counts = [daily_counts[date] for date in dates]
            
            # 简单趋势分析
            if len(counts) >= 3:
                recent_avg = np.mean(counts[-3:])
                earlier_avg = np.mean(counts[:-3]) if len(counts) > 3 else np.mean(counts[:3])
                
                if recent_avg > earlier_avg * 1.5:
                    trend = 'increasing'
                elif recent_avg < earlier_avg * 0.5:
                    trend = 'decreasing'
                else:
                    trend = 'stable'
            else:
                trend = 'stable'
            
            return {
                'trend': trend,
                'recent_average': round(np.mean(counts[-3:]), 2),
                'overall_average': round(np.mean(counts), 2),
                'max_daily_errors': max(counts),
                'min_daily_errors': min(counts)
            }
            
        except Exception as e:
            return {'trend': 'analysis_error', 'error': str(e)}
    
    def _generate_error_recommendations(self, error_patterns: Dict[str, Any]) -> List[str]:
        """生成错误处理建议"""
        recommendations = []
        
        try:
            error_summary = error_patterns.get('error_summary', {})
            most_common = error_summary.get('most_common_errors', {})
            critical_errors = error_patterns.get('critical_errors', {})
            
            # 基于常见错误类型的建议
            if 'ImportError' in most_common:
                recommendations.append("检查Python环境和依赖包安装")
            
            if 'DatabaseError' in most_common:
                recommendations.append("检查数据库连接和SQL查询语句")
            
            if 'MemoryError' in most_common:
                recommendations.append("优化内存使用，考虑增加系统内存")
            
            if 'FileNotFoundError' in most_common:
                recommendations.append("检查文件路径配置和文件权限")
            
            # 基于错误数量的建议
            total_errors = error_summary.get('total_errors', 0)
            if total_errors > 100:
                recommendations.append("错误数量过多，建议全面检查系统配置")
            
            # 基于关键错误的建议
            if critical_errors.get('count', 0) > 0:
                recommendations.append("存在关键错误，需要立即处理")
            
            if not recommendations:
                recommendations.append("错误情况正常，继续监控")
            
        except Exception as e:
            recommendations.append(f"生成建议时出错: {e}")
        
        return recommendations

    def _analyze_resource_usage(self) -> Dict[str, Any]:
        """分析资源使用"""
        try:
            print("💾 分析资源使用...")

            import psutil

            # CPU使用分析
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()

            # 内存使用分析
            memory = psutil.virtual_memory()

            # 磁盘使用分析
            disk_usage = psutil.disk_usage(str(self.project_root))

            # 进程信息
            try:
                process = psutil.Process()
                process_info = {
                    'cpu_percent': round(process.cpu_percent(), 2),
                    'memory_mb': round(process.memory_info().rss / (1024*1024), 2),
                    'memory_percent': round(process.memory_percent(), 2),
                    'num_threads': process.num_threads(),
                    'create_time': datetime.fromtimestamp(process.create_time()).isoformat()
                }
            except Exception:
                process_info = {'error': '无法获取进程信息'}

            # 文件系统分析
            project_size = self._calculate_directory_size(self.project_root)

            # 数据库文件大小
            db_path = self.project_root / 'data' / 'fucai3d.db'
            db_size_mb = round(db_path.stat().st_size / (1024*1024), 2) if db_path.exists() else 0

            # 日志文件大小
            logs_dir = self.project_root / 'logs'
            logs_size_mb = self._calculate_directory_size(logs_dir) if logs_dir.exists() else 0

            resource_analysis = {
                'system_resources': {
                    'cpu_percent': cpu_percent,
                    'cpu_count': cpu_count,
                    'memory_total_gb': round(memory.total / (1024**3), 2),
                    'memory_available_gb': round(memory.available / (1024**3), 2),
                    'memory_used_percent': memory.percent,
                    'disk_total_gb': round(disk_usage.total / (1024**3), 2),
                    'disk_free_gb': round(disk_usage.free / (1024**3), 2),
                    'disk_used_percent': round((disk_usage.used / disk_usage.total) * 100, 1)
                },
                'process_resources': process_info,
                'storage_usage': {
                    'project_size_mb': project_size,
                    'database_size_mb': db_size_mb,
                    'logs_size_mb': logs_size_mb,
                    'total_project_mb': project_size + db_size_mb + logs_size_mb
                }
            }

            # 资源使用评估
            issues = []

            if cpu_percent > 80:
                issues.append(f'CPU使用率过高: {cpu_percent}%')

            if memory.percent > 85:
                issues.append(f'内存使用率过高: {memory.percent}%')

            if disk_usage.free / disk_usage.total < 0.1:
                issues.append('磁盘空间不足')

            if db_size_mb > 1000:
                issues.append(f'数据库文件过大: {db_size_mb} MB')

            if logs_size_mb > 500:
                issues.append(f'日志文件过大: {logs_size_mb} MB')

            status = 'issues_found' if issues else 'normal'

            result = {
                'status': status,
                'resource_analysis': resource_analysis,
                'issues': issues,
                'recommendations': self._generate_resource_recommendations(resource_analysis, issues),
                'timestamp': datetime.now().isoformat()
            }

            print(f"  ✅ 资源使用分析完成: {status}")
            if issues:
                for issue in issues:
                    print(f"  ⚠️  {issue}")

            return result

        except Exception as e:
            self.logger.error(f"资源使用分析失败: {e}")
            return {'status': 'error', 'error': str(e)}

    def _calculate_directory_size(self, directory: Path) -> float:
        """计算目录大小（MB）"""
        try:
            total_size = 0
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            return round(total_size / (1024*1024), 2)
        except Exception:
            return 0.0

    def _generate_resource_recommendations(self, analysis: Dict[str, Any], issues: List[str]) -> List[str]:
        """生成资源优化建议"""
        recommendations = []

        try:
            system = analysis.get('system_resources', {})
            storage = analysis.get('storage_usage', {})

            # CPU建议
            if system.get('cpu_percent', 0) > 80:
                recommendations.append("考虑优化CPU密集型操作或升级硬件")

            # 内存建议
            if system.get('memory_used_percent', 0) > 85:
                recommendations.append("考虑增加系统内存或优化内存使用")

            # 磁盘建议
            if system.get('disk_used_percent', 0) > 90:
                recommendations.append("清理磁盘空间或扩展存储")

            # 数据库建议
            if storage.get('database_size_mb', 0) > 1000:
                recommendations.append("考虑数据库清理或归档历史数据")

            # 日志建议
            if storage.get('logs_size_mb', 0) > 500:
                recommendations.append("设置日志轮转或清理旧日志文件")

            if not recommendations:
                recommendations.append("资源使用正常，继续监控")

        except Exception as e:
            recommendations.append(f"生成建议时出错: {e}")

        return recommendations

    def _assess_prediction_quality(self) -> Dict[str, Any]:
        """评估预测质量"""
        try:
            print("🎯 评估预测质量...")

            db_path = self.project_root / 'data' / 'fucai3d.db'

            if not db_path.exists():
                return {'status': 'error', 'error': '数据库文件不存在'}

            conn = sqlite3.connect(db_path)

            quality_metrics = {}

            # 分析预测准确率趋势
            try:
                accuracy_trend_query = """
                    SELECT
                        DATE(prediction_time) as date,
                        AVG(CASE WHEN predicted_hundreds = actual_hundreds THEN 1.0 ELSE 0.0 END) as hundreds_accuracy,
                        AVG(CASE WHEN predicted_tens = actual_tens THEN 1.0 ELSE 0.0 END) as tens_accuracy,
                        AVG(CASE WHEN predicted_units = actual_units THEN 1.0 ELSE 0.0 END) as units_accuracy,
                        COUNT(*) as prediction_count
                    FROM fusion_predictions
                    WHERE prediction_time > datetime('now', '-30 days')
                    AND actual_hundreds IS NOT NULL
                    GROUP BY DATE(prediction_time)
                    ORDER BY date DESC
                """

                df_accuracy = pd.read_sql_query(accuracy_trend_query, conn)

                if not df_accuracy.empty:
                    # 计算平均准确率
                    avg_hundreds = df_accuracy['hundreds_accuracy'].mean()
                    avg_tens = df_accuracy['tens_accuracy'].mean()
                    avg_units = df_accuracy['units_accuracy'].mean()

                    # 计算趋势
                    recent_avg = df_accuracy.head(7)[['hundreds_accuracy', 'tens_accuracy', 'units_accuracy']].mean().mean()
                    earlier_avg = df_accuracy.tail(7)[['hundreds_accuracy', 'tens_accuracy', 'units_accuracy']].mean().mean()

                    trend = 'improving' if recent_avg > earlier_avg else 'declining' if recent_avg < earlier_avg else 'stable'

                    quality_metrics['accuracy_analysis'] = {
                        'average_accuracy': {
                            'hundreds': round(avg_hundreds, 4),
                            'tens': round(avg_tens, 4),
                            'units': round(avg_units, 4),
                            'overall': round((avg_hundreds + avg_tens + avg_units) / 3, 4)
                        },
                        'trend': trend,
                        'recent_vs_earlier': {
                            'recent_7days': round(recent_avg, 4),
                            'earlier_7days': round(earlier_avg, 4),
                            'improvement': round(recent_avg - earlier_avg, 4)
                        },
                        'daily_data': df_accuracy.to_dict('records')
                    }

            except Exception as e:
                quality_metrics['accuracy_analysis'] = {'error': str(e)}

            # 分析命中率分布
            try:
                hit_distribution_query = """
                    SELECT
                        hit_status,
                        COUNT(*) as count,
                        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM fusion_predictions WHERE prediction_time > datetime('now', '-30 days')), 2) as percentage
                    FROM fusion_predictions
                    WHERE prediction_time > datetime('now', '-30 days')
                    GROUP BY hit_status
                """

                df_hits = pd.read_sql_query(hit_distribution_query, conn)

                quality_metrics['hit_distribution'] = df_hits.to_dict('records') if not df_hits.empty else []

            except Exception as e:
                quality_metrics['hit_distribution'] = {'error': str(e)}

            # 分析预测一致性
            try:
                consistency_query = """
                    SELECT
                        predicted_hundreds, predicted_tens, predicted_units,
                        COUNT(*) as frequency
                    FROM fusion_predictions
                    WHERE prediction_time > datetime('now', '-30 days')
                    GROUP BY predicted_hundreds, predicted_tens, predicted_units
                    ORDER BY frequency DESC
                    LIMIT 20
                """

                df_consistency = pd.read_sql_query(consistency_query, conn)

                # 计算预测多样性
                total_predictions = df_consistency['frequency'].sum()
                top_prediction_freq = df_consistency['frequency'].iloc[0] if not df_consistency.empty else 0
                diversity_score = 1 - (top_prediction_freq / total_predictions) if total_predictions > 0 else 0

                quality_metrics['prediction_consistency'] = {
                    'diversity_score': round(diversity_score, 4),
                    'most_frequent_predictions': df_consistency.head(10).to_dict('records'),
                    'total_unique_predictions': len(df_consistency)
                }

            except Exception as e:
                quality_metrics['prediction_consistency'] = {'error': str(e)}

            conn.close()

            # 评估整体预测质量
            overall_quality = self._evaluate_overall_quality(quality_metrics)

            result = {
                'status': 'analyzed',
                'overall_quality': overall_quality,
                'quality_metrics': quality_metrics,
                'recommendations': self._generate_quality_recommendations(quality_metrics, overall_quality),
                'timestamp': datetime.now().isoformat()
            }

            print(f"  ✅ 预测质量评估完成: {overall_quality['grade']}")

            return result

        except Exception as e:
            self.logger.error(f"预测质量评估失败: {e}")
            return {'status': 'error', 'error': str(e)}

    def _evaluate_overall_quality(self, quality_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """评估整体质量"""
        try:
            accuracy_analysis = quality_metrics.get('accuracy_analysis', {})

            if 'error' in accuracy_analysis:
                return {'grade': 'unknown', 'score': 0.0, 'reason': '数据不足'}

            avg_accuracy = accuracy_analysis.get('average_accuracy', {})
            overall_accuracy = avg_accuracy.get('overall', 0.0)

            # 评分标准
            if overall_accuracy >= 0.35:
                grade = 'excellent'
                score = 0.9
            elif overall_accuracy >= 0.30:
                grade = 'good'
                score = 0.8
            elif overall_accuracy >= 0.25:
                grade = 'fair'
                score = 0.6
            elif overall_accuracy >= 0.20:
                grade = 'poor'
                score = 0.4
            else:
                grade = 'very_poor'
                score = 0.2

            # 考虑趋势
            trend = accuracy_analysis.get('trend', 'stable')
            if trend == 'improving':
                score += 0.1
            elif trend == 'declining':
                score -= 0.1

            score = max(0.0, min(1.0, score))

            return {
                'grade': grade,
                'score': round(score, 2),
                'overall_accuracy': overall_accuracy,
                'trend': trend
            }

        except Exception as e:
            return {'grade': 'error', 'score': 0.0, 'error': str(e)}

    def _generate_quality_recommendations(self, quality_metrics: Dict[str, Any],
                                        overall_quality: Dict[str, Any]) -> List[str]:
        """生成质量改进建议"""
        recommendations = []

        try:
            grade = overall_quality.get('grade', 'unknown')
            trend = overall_quality.get('trend', 'stable')

            # 基于等级的建议
            if grade in ['very_poor', 'poor']:
                recommendations.append("预测准确率较低，建议重新训练模型")
                recommendations.append("检查特征工程和数据预处理流程")
                recommendations.append("考虑调整模型参数或尝试不同算法")

            elif grade == 'fair':
                recommendations.append("预测质量一般，有改进空间")
                recommendations.append("优化特征选择和模型融合策略")

            elif grade in ['good', 'excellent']:
                recommendations.append("预测质量良好，继续保持")
                recommendations.append("可以尝试微调以进一步提升")

            # 基于趋势的建议
            if trend == 'declining':
                recommendations.append("预测质量呈下降趋势，需要关注")
                recommendations.append("检查数据质量和模型是否需要重训练")

            elif trend == 'improving':
                recommendations.append("预测质量在改善，继续当前策略")

            # 基于一致性的建议
            consistency = quality_metrics.get('prediction_consistency', {})
            diversity_score = consistency.get('diversity_score', 0)

            if diversity_score < 0.5:
                recommendations.append("预测多样性不足，可能存在过拟合")

            if not recommendations:
                recommendations.append("预测质量分析正常")

        except Exception as e:
            recommendations.append(f"生成建议时出错: {e}")

        return recommendations

    def _analyze_data_quality(self) -> Dict[str, Any]:
        """分析数据质量"""
        try:
            print("📊 分析数据质量...")

            db_path = self.project_root / 'data' / 'fucai3d.db'

            if not db_path.exists():
                return {'status': 'error', 'error': '数据库文件不存在'}

            conn = sqlite3.connect(db_path)

            data_quality = {}

            # 检查历史数据完整性
            try:
                data_completeness_query = """
                    SELECT
                        COUNT(*) as total_records,
                        COUNT(CASE WHEN hundreds IS NULL THEN 1 END) as missing_hundreds,
                        COUNT(CASE WHEN tens IS NULL THEN 1 END) as missing_tens,
                        COUNT(CASE WHEN units IS NULL THEN 1 END) as missing_units,
                        MIN(issue) as earliest_issue,
                        MAX(issue) as latest_issue
                    FROM lottery_data
                """

                cursor = conn.cursor()
                cursor.execute(data_completeness_query)
                completeness_result = cursor.fetchone()

                if completeness_result:
                    total, missing_h, missing_t, missing_u, earliest, latest = completeness_result

                    data_quality['data_completeness'] = {
                        'total_records': total,
                        'missing_data': {
                            'hundreds': missing_h,
                            'tens': missing_t,
                            'units': missing_u
                        },
                        'completeness_rate': round((total - max(missing_h, missing_t, missing_u)) / total * 100, 2) if total > 0 else 0,
                        'date_range': {
                            'earliest': earliest,
                            'latest': latest
                        }
                    }

            except Exception as e:
                data_quality['data_completeness'] = {'error': str(e)}

            # 检查数据一致性
            try:
                consistency_query = """
                    SELECT
                        COUNT(*) as total,
                        COUNT(CASE WHEN hundreds < 0 OR hundreds > 9 THEN 1 END) as invalid_hundreds,
                        COUNT(CASE WHEN tens < 0 OR tens > 9 THEN 1 END) as invalid_tens,
                        COUNT(CASE WHEN units < 0 OR units > 9 THEN 1 END) as invalid_units
                    FROM lottery_data
                """

                cursor.execute(consistency_query)
                consistency_result = cursor.fetchone()

                if consistency_result:
                    total, invalid_h, invalid_t, invalid_u = consistency_result

                    data_quality['data_consistency'] = {
                        'total_records': total,
                        'invalid_data': {
                            'hundreds': invalid_h,
                            'tens': invalid_t,
                            'units': invalid_u
                        },
                        'consistency_rate': round((total - max(invalid_h, invalid_t, invalid_u)) / total * 100, 2) if total > 0 else 0
                    }

            except Exception as e:
                data_quality['data_consistency'] = {'error': str(e)}

            # 检查预测数据质量
            try:
                prediction_quality_query = """
                    SELECT
                        COUNT(*) as total_predictions,
                        COUNT(CASE WHEN actual_hundreds IS NULL THEN 1 END) as missing_actual,
                        COUNT(CASE WHEN confidence_score IS NULL THEN 1 END) as missing_confidence,
                        AVG(confidence_score) as avg_confidence
                    FROM fusion_predictions
                    WHERE prediction_time > datetime('now', '-30 days')
                """

                cursor.execute(prediction_quality_query)
                pred_quality_result = cursor.fetchone()

                if pred_quality_result:
                    total_pred, missing_actual, missing_conf, avg_conf = pred_quality_result

                    data_quality['prediction_data_quality'] = {
                        'total_predictions': total_pred,
                        'missing_actual_results': missing_actual,
                        'missing_confidence_scores': missing_conf,
                        'average_confidence': round(avg_conf, 4) if avg_conf else None,
                        'data_availability_rate': round((total_pred - missing_actual) / total_pred * 100, 2) if total_pred > 0 else 0
                    }

            except Exception as e:
                data_quality['prediction_data_quality'] = {'error': str(e)}

            conn.close()

            # 评估整体数据质量
            quality_issues = []

            completeness = data_quality.get('data_completeness', {})
            if completeness.get('completeness_rate', 100) < 95:
                quality_issues.append('历史数据不完整')

            consistency = data_quality.get('data_consistency', {})
            if consistency.get('consistency_rate', 100) < 99:
                quality_issues.append('数据一致性问题')

            pred_quality = data_quality.get('prediction_data_quality', {})
            if pred_quality.get('data_availability_rate', 100) < 80:
                quality_issues.append('预测结果数据缺失')

            overall_status = 'issues_found' if quality_issues else 'good'

            result = {
                'status': overall_status,
                'data_quality': data_quality,
                'quality_issues': quality_issues,
                'recommendations': self._generate_data_quality_recommendations(data_quality, quality_issues),
                'timestamp': datetime.now().isoformat()
            }

            print(f"  ✅ 数据质量分析完成: {overall_status}")
            if quality_issues:
                for issue in quality_issues:
                    print(f"  ⚠️  {issue}")

            return result

        except Exception as e:
            self.logger.error(f"数据质量分析失败: {e}")
            return {'status': 'error', 'error': str(e)}

    def _generate_data_quality_recommendations(self, data_quality: Dict[str, Any],
                                             issues: List[str]) -> List[str]:
        """生成数据质量改进建议"""
        recommendations = []

        try:
            # 基于完整性的建议
            completeness = data_quality.get('data_completeness', {})
            if completeness.get('completeness_rate', 100) < 95:
                recommendations.append("补充缺失的历史数据")
                recommendations.append("建立数据质量监控机制")

            # 基于一致性的建议
            consistency = data_quality.get('data_consistency', {})
            if consistency.get('consistency_rate', 100) < 99:
                recommendations.append("清理无效数据记录")
                recommendations.append("加强数据验证规则")

            # 基于预测数据的建议
            pred_quality = data_quality.get('prediction_data_quality', {})
            if pred_quality.get('data_availability_rate', 100) < 80:
                recommendations.append("改进预测结果记录机制")
                recommendations.append("确保实际结果及时更新")

            if not recommendations:
                recommendations.append("数据质量良好，继续维护")

        except Exception as e:
            recommendations.append(f"生成建议时出错: {e}")

        return recommendations

    def _analyze_system_configuration(self) -> Dict[str, Any]:
        """分析系统配置"""
        try:
            print("⚙️  分析系统配置...")

            config_analysis = {}

            # 检查P9配置文件
            p9_config_path = self.project_root / 'config' / 'p9_config.json'
            if p9_config_path.exists():
                try:
                    with open(p9_config_path, 'r', encoding='utf-8') as f:
                        p9_config = json.load(f)

                    config_analysis['p9_config'] = {
                        'status': 'valid',
                        'config_keys': list(p9_config.keys()),
                        'optimization_enabled': p9_config.get('optimization_manager', {}).get('enabled', False),
                        'monitoring_enabled': p9_config.get('performance_monitor', {}).get('enabled', False)
                    }

                except Exception as e:
                    config_analysis['p9_config'] = {'status': 'error', 'error': str(e)}
            else:
                config_analysis['p9_config'] = {'status': 'missing'}

            # 检查Python环境
            import sys
            config_analysis['python_environment'] = {
                'python_version': sys.version,
                'python_path': sys.executable,
                'installed_packages': self._get_installed_packages()
            }

            # 检查关键目录权限
            directories_to_check = ['data', 'logs', 'config', 'src/optimization']
            directory_permissions = {}

            for dir_name in directories_to_check:
                dir_path = self.project_root / dir_name
                if dir_path.exists():
                    directory_permissions[dir_name] = {
                        'exists': True,
                        'readable': os.access(dir_path, os.R_OK),
                        'writable': os.access(dir_path, os.W_OK),
                        'executable': os.access(dir_path, os.X_OK)
                    }
                else:
                    directory_permissions[dir_name] = {'exists': False}

            config_analysis['directory_permissions'] = directory_permissions

            # 评估配置状态
            config_issues = []

            if config_analysis['p9_config']['status'] != 'valid':
                config_issues.append('P9配置文件问题')

            for dir_name, perms in directory_permissions.items():
                if not perms.get('exists', False):
                    config_issues.append(f'目录不存在: {dir_name}')
                elif not perms.get('writable', False):
                    config_issues.append(f'目录不可写: {dir_name}')

            overall_status = 'issues_found' if config_issues else 'good'

            result = {
                'status': overall_status,
                'config_analysis': config_analysis,
                'config_issues': config_issues,
                'recommendations': self._generate_config_recommendations(config_analysis, config_issues),
                'timestamp': datetime.now().isoformat()
            }

            print(f"  ✅ 系统配置分析完成: {overall_status}")
            if config_issues:
                for issue in config_issues:
                    print(f"  ⚠️  {issue}")

            return result

        except Exception as e:
            self.logger.error(f"系统配置分析失败: {e}")
            return {'status': 'error', 'error': str(e)}

    def _get_installed_packages(self) -> Dict[str, str]:
        """获取已安装的Python包"""
        try:
            import pkg_resources

            packages = {}
            for package in pkg_resources.working_set:
                packages[package.project_name] = package.version

            return packages

        except Exception:
            return {'error': '无法获取包信息'}

    def _generate_config_recommendations(self, config_analysis: Dict[str, Any],
                                       issues: List[str]) -> List[str]:
        """生成配置优化建议"""
        recommendations = []

        try:
            # P9配置建议
            p9_config = config_analysis.get('p9_config', {})
            if p9_config.get('status') == 'missing':
                recommendations.append("创建P9配置文件")
            elif p9_config.get('status') == 'error':
                recommendations.append("修复P9配置文件格式错误")

            # 权限建议
            permissions = config_analysis.get('directory_permissions', {})
            for dir_name, perms in permissions.items():
                if not perms.get('exists', False):
                    recommendations.append(f"创建缺失目录: {dir_name}")
                elif not perms.get('writable', False):
                    recommendations.append(f"修复目录写权限: {dir_name}")

            if not recommendations:
                recommendations.append("系统配置正常")

        except Exception as e:
            recommendations.append(f"生成建议时出错: {e}")

        return recommendations

    def _generate_diagnostic_report(self) -> Dict[str, Any]:
        """生成诊断报告"""
        try:
            # 收集所有诊断结果
            all_results = {}

            for category, result in self.diagnostic_results.items():
                if category != 'diagnostic_report' and isinstance(result, dict):
                    status = result.get('status', 'unknown')
                    all_results[category] = status

            # 统计状态
            normal_count = sum(1 for status in all_results.values() if status in ['normal', 'good', 'analyzed'])
            issues_count = sum(1 for status in all_results.values() if status == 'issues_found')
            error_count = sum(1 for status in all_results.values() if status == 'error')
            total_count = len(all_results)

            # 确定整体健康状态
            if error_count > 0:
                overall_health = 'critical'
                health_score = 0.3
            elif issues_count > total_count / 2:
                overall_health = 'poor'
                health_score = 0.5
            elif issues_count > 0:
                overall_health = 'fair'
                health_score = 0.7
            else:
                overall_health = 'good'
                health_score = 0.9

            # 收集所有建议
            all_recommendations = []
            for result in self.diagnostic_results.values():
                if isinstance(result, dict) and 'recommendations' in result:
                    all_recommendations.extend(result['recommendations'])

            # 去重建议
            unique_recommendations = list(set(all_recommendations))

            # 生成优先级建议
            priority_recommendations = []
            if error_count > 0:
                priority_recommendations.append("立即处理系统错误")
            if issues_count > 0:
                priority_recommendations.append("解决发现的问题")

            report = {
                'overall_health': overall_health,
                'health_score': health_score,
                'summary': {
                    'total_categories': total_count,
                    'normal_categories': normal_count,
                    'issues_categories': issues_count,
                    'error_categories': error_count
                },
                'category_status': all_results,
                'all_recommendations': unique_recommendations,
                'priority_recommendations': priority_recommendations,
                'generated_at': datetime.now().isoformat()
            }

            return report

        except Exception as e:
            return {'error': str(e)}

    def _print_diagnostic_summary(self):
        """打印诊断摘要"""
        print("\n" + "="*60)
        print("🔬 P9系统诊断报告摘要")
        print("="*60)

        report = self.diagnostic_results.get('diagnostic_report', {})

        # 整体健康状态
        health = report.get('overall_health', 'unknown')
        score = report.get('health_score', 0.0)

        health_icon = {
            'good': '🟢',
            'fair': '🟡',
            'poor': '🟠',
            'critical': '🔴'
        }.get(health, '⚪')

        print(f"\n{health_icon} 整体健康状态: {health.upper()}")
        print(f"💯 健康评分: {score * 100:.1f}%")

        # 分类统计
        summary = report.get('summary', {})
        print(f"\n📊 诊断分类统计:")
        print(f"  🟢 正常: {summary.get('normal_categories', 0)}")
        print(f"  🟡 有问题: {summary.get('issues_categories', 0)}")
        print(f"  🔴 错误: {summary.get('error_categories', 0)}")
        print(f"  📊 总计: {summary.get('total_categories', 0)}")

        # 优先建议
        priority_recs = report.get('priority_recommendations', [])
        if priority_recs:
            print(f"\n🚨 优先处理:")
            for i, rec in enumerate(priority_recs, 1):
                print(f"  {i}. {rec}")

        # 详细建议
        all_recs = report.get('all_recommendations', [])
        if all_recs:
            print(f"\n💡 所有建议 (共{len(all_recs)}条):")
            for i, rec in enumerate(all_recs[:10], 1):  # 只显示前10条
                print(f"  {i}. {rec}")

            if len(all_recs) > 10:
                print(f"  ... 还有 {len(all_recs) - 10} 条建议")

        print("\n" + "="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='P9系统诊断工具')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--json', '-j', action='store_true', help='JSON格式输出')
    parser.add_argument('--category', '-c', help='诊断特定类别')
    parser.add_argument('--output', '-o', help='输出文件路径')

    args = parser.parse_args()

    try:
        diagnostics = P9SystemDiagnostics(verbose=args.verbose)

        if args.category:
            # 诊断特定类别
            category_methods = {
                'performance': diagnostics._analyze_performance_bottlenecks,
                'errors': diagnostics._identify_error_patterns,
                'resources': diagnostics._analyze_resource_usage,
                'quality': diagnostics._assess_prediction_quality,
                'data': diagnostics._analyze_data_quality,
                'config': diagnostics._analyze_system_configuration
            }

            if args.category in category_methods:
                result = category_methods[args.category]()

                if args.output:
                    with open(args.output, 'w', encoding='utf-8') as f:
                        json.dump(result, f, indent=2, ensure_ascii=False)
                    print(f"诊断结果已保存到: {args.output}")
                elif args.json:
                    print(json.dumps(result, indent=2, ensure_ascii=False))
                else:
                    print(f"\n{args.category.title()} 诊断结果:")
                    print(json.dumps(result, indent=2, ensure_ascii=False))
            else:
                print(f"❌ 未知诊断类别: {args.category}")
                print(f"可用类别: {', '.join(category_methods.keys())}")
                sys.exit(1)
        else:
            # 执行完整诊断
            results = diagnostics.run_full_diagnostics()

            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
                print(f"完整诊断结果已保存到: {args.output}")
            elif args.json:
                print(json.dumps(results, indent=2, ensure_ascii=False))

            # 根据健康状态设置退出码
            report = results.get('diagnostic_report', {})
            health = report.get('overall_health', 'unknown')

            if health == 'critical':
                sys.exit(3)
            elif health == 'poor':
                sys.exit(2)
            elif health == 'fair':
                sys.exit(1)
            else:
                sys.exit(0)

    except KeyboardInterrupt:
        print("\n⚠️  诊断被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 诊断过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
